using System.Reflection;
using System.Runtime.InteropServices;

// General Information about an assembly is controlled through the following 
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.

[assembly: AssemblyTitle("LibNoise.Unity")]
[assembly: AssemblyProduct("LibNoise.Unity")]
[assembly:
    AssemblyDescription("C# Unity Port of LibNoise, Ported from BigBlackBlock Gamestudio port of LibNoise to XNA")]
[assembly: AssemblyCompany("None")]
[assembly:
    AssemblyCopyright(
        "LibUnity.Xna Copyright © Jason <PERSON> 2003-2007, 2010 BigBlackBlock Gamestudio, LibNoise.Unity Copyright © 2010 Unity Commons, distributed under the terms on the Lesser GPL"
        )]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// Setting ComVisible to false makes the types in this assembly not visible 
// to COM components.  If you need to access a type in this assembly from 
// COM, set the ComVisible attribute to true on that type.

[assembly: ComVisible(false)]

// The following GUID is for the ID of the typelib if this project is exposed to COM

[assembly: Guid("aa606d44-f8c3-47aa-a2b5-9156119ff843")]

// Version information for an assembly consists of the following four values:
//
//      Major Version
//      Minor Version 
//      Build Number
//      Revision
//

[assembly: AssemblyVersion("1.0.0.0")]