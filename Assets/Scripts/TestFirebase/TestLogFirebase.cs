using b100SDK.Scripts.Analytic;
using b100SDK.Scripts.UI.Components.Button;
using TMPro;
using UnityEngine;

namespace TestFirebase
{
    public class TestLogFirebase : MonoBehaviour
    {

        public TMP_InputField levelInput;
        public TMP_InputField expInput;

        public BhButton logLevelButton;
        public BhButton logExpButton;

        private void OnEnable()
        {
            logLevelButton.onClick.AddListener(LogLevel);
            logExpButton.onClick.AddListener(LogExp);
        }
        
        private void OnDisable()
        {
            logLevelButton.onClick.RemoveListener(LogLevel);
            logExpButton.onClick.RemoveListener(LogExp);
        }


        void LogLevel()
        {
            var level = levelInput.text;
            
            AnalyticManager.LogEvent("LevelTracking", "level",level);
			Debug.Log("Level: " + level);
        }

        void LogExp()
        {
            var exp = expInput.text;

            AnalyticManager.LogEvent("ExpTracking", "exp", exp);
            Debug.Log("Exp: " + exp);
        }
    }
}