using System;
using b100SDK.Scripts.Base;
//using b100SDK.Scripts.PushNotification;
using b100SDK.Scripts.UI.Components.Button;
using TMPro;
using UnityEngine;

namespace TestNotification
{
    public class TestNotification : BhMonoBehavior
    {
        public TMP_InputField id;
        public TMP_InputField notificationName;
        public TMP_InputField title;
        public TMP_InputField content;
        public TMP_InputField time;
        public TMP_InputField loop;

        public BhButton button;
        public BhButton buttonCancel;


        private void OnEnable()
        {
            button.onClick.AddListener(ScheduleNoti);
            buttonCancel.onClick.AddListener(Cancel);
        }
        private void OnDisable()
        {
            button.onClick.RemoveListener(ScheduleNoti);
            buttonCancel.onClick.RemoveListener(Cancel);
        }



        void ScheduleNoti()
        {
            
            TimeSpan parsedTime;
            if (TimeSpan.TryParseExact(time.text, @"hh\:mm", null, out parsedTime))
            {
                Debug.Log("Parsed Time: " + parsedTime.ToString(@"hh\:mm"));
            }
            else
            {
                Debug.Log("Invalid time format. Please enter in HH:MM format (e.g., 14:30)");
            }

            var now = DateTime.Now;
            var scheduleTime = new DateTime(now.Year, now.Month, now.Day, 0, 0, 1);
             scheduleTime = scheduleTime.Add(parsedTime);

             /*
             //PushNotificationManager.Instance.ScheduleNotification(int.Parse(id.text), notificationName.text,
                 title.text, content.text, scheduleTime, int.Parse(loop.text));*/


        }

        void Cancel()
        {
            //PushNotificationManager.Instance.CancelAllNotifications();
            Debug.LogWarning("Cancelling notification");
        }
    }
}