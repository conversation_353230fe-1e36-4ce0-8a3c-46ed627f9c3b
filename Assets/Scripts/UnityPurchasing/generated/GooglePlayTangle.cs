// WARNING: Do not modify! Generated file.

namespace UnityEngine.Purchasing.Security {
    public class GooglePlayTangle
    {
        private static byte[] data = System.Convert.FromBase64String("DfZaKyAmnmVQ2MjVpQfxi78cUwCvLCItHa8sJy+vLCwtnLauNb2pvZIOFI+AGFsya62t0oG27D8P35U/2nnKzNvNTprsFuhB5gGll4ESDIYdrywPHSArJAerZavaICwsLCgtLn6JhdOi0wXicARbOUuvzb5wWeu17PH4Yhpx+EgsCF5D80uPfSJyLPf5SgtPJ+9ShXtnjuqexOHT0qF+c9f1Z4YzLYrGVEW9Xj6sVrG3tcl3zITgrqF71G2onPKiMAi2zxYGkWDUJdq8s7+zCxa/NpIUtZgHH+zdsdxjlVFKot5/6PtlgPhxi6dp74kOynC2aP8JyliGrUsOO9h8vwWkgMxdgCJR+sktpuGXalQivPyPdIPrc2KmRnBW88zQJi8uLC0s");
        private static int[] order = new int[] { 10,1,13,6,10,8,12,13,11,9,13,11,12,13,14 };
        private static int key = 45;

        public static readonly bool IsPopulated = true;

        public static byte[] Data() {
        	if (IsPopulated == false)
        		return null;
            return Obfuscator.DeObfuscate(data, order, key);
        }
    }
}
